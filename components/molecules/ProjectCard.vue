<template>
  <!-- Large Project Card for Desktop -->
  <div
    v-if="variant === 'large'"
    ref="cardElement"
    class="rounded-3xl bg-cover bg-center relative overflow-hidden group cursor-pointer"
    :class="[
      hoverEffect === 'height' ? 'min-h-[400px] md:min-h-[450px] lg:min-h-[400px] transition-all duration-700 ease-out hover:min-h-[500px] md:hover:min-h-[550px] lg:hover:min-h-[500px] hover:shadow-2xl' :
      hoverEffect === 'flip' ? 'min-h-[400px] md:min-h-[450px] lg:min-h-[400px] [perspective:1000px]' :
      'min-h-[400px] md:min-h-[450px] lg:min-h-[400px] transition-all duration-500 ease-in-out hover:shadow-2xl'
    ]"
    :style="backgroundStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Flip Card Front -->
    <div
      v-if="hoverEffect === 'flip'"
      ref="cardFront"
      class="absolute inset-0 [backface-visibility:hidden] transition-transform duration-700 ease-out"
      :class="{ '[transform:rotateY(180deg)]': isFlipped }"
    >
      <!-- Glass effect overlay for large card -->
      <div
        class="absolute bottom-8 md:bottom-10 lg:bottom-8 right-8 md:right-10 lg:right-12 w-[80%] md:w-[85%] lg:w-[600px] p-6 md:p-8 rounded-3xl flex flex-col md:flex-row justify-between items-start md:items-end transition-all duration-500 ease-in-out gap-4 md:gap-6 lg:gap-4"
        style="background-color: rgba(255, 255, 255, 0.5); backdrop-filter: blur(6px);"
      >
        <div class="mb-4 md:mb-0">
          <h3 class="h4-bold md:h3-bold lg:h4-bold text-[var(--color-primary-1)]">{{ name }}</h3>
          <p class="body-2-bold md:body-1-bold lg:body-2-bold text-[var(--color-primary-1)]">{{ category }}</p>
        </div>
        <Button
          variant="secondary"
          class="bg-white text-[var(--color-primary-2)] py-3 md:py-4 lg:py-3 px-5 md:px-6 lg:px-5 rounded-full body-2-bold md:body-1-bold lg:body-2-bold hover:bg-[var(--color-neutral-6)] transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl min-h-[44px] md:min-h-[48px] lg:min-h-[44px]"
          @click="handleViewMore"
        >
          View more
        </Button>
      </div>
    </div>

    <!-- Flip Card Back -->
    <div
      v-if="hoverEffect === 'flip'"
      ref="cardBack"
      class="absolute inset-0 [backface-visibility:hidden] [transform:rotateY(180deg)] transition-transform duration-700 ease-out bg-[var(--color-neutral-1)] p-6 md:p-8 flex flex-col justify-between"
      :class="{ '[transform:rotateY(0deg)]': isFlipped }"
    >
      <div>
        <h3 class="h4-bold md:h3-bold text-[var(--color-neutral-7)] mb-4">{{ name }}</h3>
        <p class="body-2-regular md:body-1-regular text-[var(--color-neutral-5)] mb-6">{{ description || 'Detailed project information and technical specifications.' }}</p>
        
        <!-- Tech Stack -->
        <div v-if="techStack && techStack.length" class="mb-6">
          <h4 class="label-1 text-[var(--color-primary-2)] mb-3">Tech Stack</h4>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="tech in techStack"
              :key="tech"
              class="px-3 py-1 bg-[var(--color-primary-3)] text-[var(--color-neutral-1)] body-3-bold rounded-full"
            >
              {{ tech }}
            </span>
          </div>
        </div>

        <!-- Project Stats -->
        <div v-if="duration || achievements" class="mb-6">
          <h4 class="label-1 text-[var(--color-primary-2)] mb-3">Project Details</h4>
          <div class="space-y-2">
            <p v-if="duration" class="body-3-regular text-[var(--color-neutral-5)]">
              <span class="body-3-bold text-[var(--color-neutral-7)]">Duration:</span> {{ duration }}
            </p>
            <p v-if="achievements" class="body-3-regular text-[var(--color-neutral-5)]">
              <span class="body-3-bold text-[var(--color-neutral-7)]">Key Achievement:</span> {{ achievements }}
            </p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-3">
        <Button
          variant="primary"
          class="flex-1 py-3 px-5 rounded-full body-2-bold min-h-[44px]"
          @click="handleViewMore"
        >
          View Project
        </Button>
        <Button
          v-if="githubUrl"
          variant="outline"
          class="flex-1 py-3 px-5 rounded-full body-2-bold min-h-[44px]"
          @click="handleGithubClick"
        >
          GitHub
        </Button>
      </div>
    </div>

    <!-- Non-flip card content -->
    <div v-if="hoverEffect !== 'flip'">
      <!-- Glass effect overlay for large card -->
      <div
        class="absolute bottom-8 md:bottom-10 lg:bottom-8 right-8 md:right-10 lg:right-12 w-[80%] md:w-[85%] lg:w-[600px] p-6 md:p-8 rounded-3xl flex flex-col md:flex-row justify-between items-start md:items-end transition-all duration-500 ease-in-out group-hover:bottom-12 md:group-hover:bottom-16 group-hover:shadow-2xl gap-4 md:gap-6 lg:gap-4"
        style="background-color: rgba(255, 255, 255, 0.5); backdrop-filter: blur(6px);"
      >
        <div class="mb-4 md:mb-0">
          <h3 class="h4-bold md:h3-bold lg:h4-bold text-[var(--color-primary-1)]">{{ name }}</h3>
          <p class="body-2-bold md:body-1-bold lg:body-2-bold text-[var(--color-primary-1)]">{{ category }}</p>
        </div>
        <Button
          variant="secondary"
          class="bg-white text-[var(--color-primary-2)] py-3 md:py-4 lg:py-3 px-5 md:px-6 lg:px-5 rounded-full body-2-bold md:body-1-bold lg:body-2-bold hover:bg-[var(--color-neutral-6)] transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl min-h-[44px] md:min-h-[48px] lg:min-h-[44px]"
          @click="handleViewMore"
        >
          View more
        </Button>
      </div>

      <!-- Height expansion content -->
      <div
        v-if="hoverEffect === 'height'"
        ref="expandedContent"
        class="absolute bottom-0 left-0 right-0 bg-[var(--color-neutral-1)] p-6 md:p-8 transform translate-y-full group-hover:translate-y-0 transition-transform duration-700 ease-out opacity-0 group-hover:opacity-100"
      >
        <!-- Tech Stack Badges -->
        <div v-if="techStack && techStack.length" class="mb-4">
          <h4 class="label-2 text-[var(--color-primary-2)] mb-2">Technologies</h4>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="tech in techStack"
              :key="tech"
              class="px-3 py-1 bg-[var(--color-primary-3)] text-[var(--color-neutral-1)] body-3-bold rounded-full"
            >
              {{ tech }}
            </span>
          </div>
        </div>

        <!-- Project Duration and Achievements -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-if="duration">
            <h5 class="body-3-bold text-[var(--color-neutral-7)] mb-1">Duration</h5>
            <p class="body-3-regular text-[var(--color-neutral-5)]">{{ duration }}</p>
          </div>
          <div v-if="achievements">
            <h5 class="body-3-bold text-[var(--color-neutral-7)] mb-1">Key Achievement</h5>
            <p class="body-3-regular text-[var(--color-neutral-5)]">{{ achievements }}</p>
          </div>
        </div>
      </div>

      <!-- Sliding panel content -->
      <div
        v-if="hoverEffect === 'slide'"
        ref="slidingPanel"
        class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-[var(--color-neutral-1)] via-[var(--color-neutral-1)] to-transparent p-6 md:p-8 transform translate-y-full transition-transform duration-500 ease-out"
      >
        <div class="space-y-4">
          <!-- Project Metrics -->
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
            <div v-if="githubStats?.stars !== undefined" class="bg-[var(--color-neutral-2)] rounded-lg p-3">
              <div class="h5-bold text-[var(--color-primary-2)]">{{ githubStats.stars }}</div>
              <div class="body-3-regular text-[var(--color-neutral-5)]">Stars</div>
            </div>
            <div v-if="githubStats?.forks !== undefined" class="bg-[var(--color-neutral-2)] rounded-lg p-3">
              <div class="h5-bold text-[var(--color-primary-2)]">{{ githubStats.forks }}</div>
              <div class="body-3-regular text-[var(--color-neutral-5)]">Forks</div>
            </div>
            <div v-if="duration" class="bg-[var(--color-neutral-2)] rounded-lg p-3">
              <div class="h5-bold text-[var(--color-primary-2)]">{{ duration }}</div>
              <div class="body-3-regular text-[var(--color-neutral-5)]">Duration</div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="flex gap-3">
            <Button
              variant="primary"
              class="flex-1 py-2 px-4 rounded-full body-3-bold"
              @click="handleViewMore"
            >
              View Details
            </Button>
            <Button
              v-if="githubUrl"
              variant="outline"
              class="flex-1 py-2 px-4 rounded-full body-3-bold"
              @click="handleGithubClick"
            >
              GitHub
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Small Project Card for Desktop and Mobile -->
  <div
    v-else
    ref="cardElement"
    class="rounded-3xl bg-cover bg-center relative overflow-hidden group cursor-pointer"
    :class="[
      hoverEffect === 'height' ? 'min-h-[360px] md:min-h-[400px] lg:min-h-[360px] transition-all duration-700 ease-out hover:min-h-[460px] md:hover:min-h-[500px] lg:hover:min-h-[460px] hover:shadow-xl' :
      hoverEffect === 'flip' ? 'min-h-[360px] md:min-h-[400px] lg:min-h-[360px] [perspective:1000px]' :
      'min-h-[360px] md:min-h-[400px] lg:min-h-[360px] transition-all duration-300 ease-in-out hover:shadow-xl'
    ]"
    :style="backgroundStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Flip Card Front -->
    <div
      v-if="hoverEffect === 'flip'"
      ref="cardFront"
      class="absolute inset-0 [backface-visibility:hidden] transition-transform duration-700 ease-out"
      :class="{ '[transform:rotateY(180deg)]': isFlipped }"
    >
      <!-- Content overlay for small cards -->
      <div
        class="absolute inset-0 flex flex-col justify-between p-6 md:p-8"
        :style="overlayStyle"
      >
        <!-- Content positioned at bottom -->
        <div class="flex-grow"></div>
        <div class="text-white">
          <h3 class="h4-bold md:h3-bold lg:h4-bold mb-2 md:mb-3 lg:mb-2">{{ name }}</h3>
          <p class="body-2-bold md:body-1-bold lg:body-2-bold mb-4 md:mb-6 lg:mb-4">{{ category }}</p>
          <Button
            variant="secondary"
            class="bg-white text-[var(--color-primary-2)] py-3 md:py-4 lg:py-3 px-5 md:px-6 lg:px-5 rounded-full body-2-bold md:body-1-bold lg:body-2-bold hover:bg-[var(--color-neutral-6)] transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl min-h-[44px] md:min-h-[48px] lg:min-h-[44px]"
            @click="handleViewMore"
          >
            View more
          </Button>
        </div>
      </div>
    </div>

    <!-- Flip Card Back -->
    <div
      v-if="hoverEffect === 'flip'"
      ref="cardBack"
      class="absolute inset-0 [backface-visibility:hidden] [transform:rotateY(180deg)] transition-transform duration-700 ease-out bg-[var(--color-neutral-1)] p-6 md:p-8 flex flex-col justify-between"
      :class="{ '[transform:rotateY(0deg)]': isFlipped }"
    >
      <div>
        <h3 class="h4-bold md:h3-bold text-[var(--color-neutral-7)] mb-3">{{ name }}</h3>
        <p class="body-3-regular text-[var(--color-neutral-5)] mb-4">{{ description || 'Detailed project information and specifications.' }}</p>
        
        <!-- Tech Stack -->
        <div v-if="techStack && techStack.length" class="mb-4">
          <h4 class="label-2 text-[var(--color-primary-2)] mb-2">Tech Stack</h4>
          <div class="flex flex-wrap gap-1">
            <span
              v-for="tech in techStack.slice(0, 4)"
              :key="tech"
              class="px-2 py-1 bg-[var(--color-primary-3)] text-[var(--color-neutral-1)] body-3-bold rounded-full text-xs"
            >
              {{ tech }}
            </span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-2">
        <Button
          variant="primary"
          class="flex-1 py-2 px-4 rounded-full body-3-bold"
          @click="handleViewMore"
        >
          View Project
        </Button>
        <Button
          v-if="githubUrl"
          variant="outline"
          class="flex-1 py-2 px-4 rounded-full body-3-bold"
          @click="handleGithubClick"
        >
          GitHub
        </Button>
      </div>
    </div>

    <!-- Non-flip card content -->
    <div v-if="hoverEffect !== 'flip'">
      <!-- Content overlay for small cards -->
      <div
        class="absolute inset-0 flex flex-col justify-between p-6 md:p-8"
        :style="overlayStyle"
      >
        <!-- Content positioned at bottom -->
        <div class="flex-grow"></div>
        <div class="text-white">
          <h3 class="h4-bold md:h3-bold lg:h4-bold mb-2 md:mb-3 lg:mb-2">{{ name }}</h3>
          <p class="body-2-bold md:body-1-bold lg:body-2-bold mb-4 md:mb-6 lg:mb-4">{{ category }}</p>
          <Button
            variant="secondary"
            class="bg-white text-[var(--color-primary-2)] py-3 md:py-4 lg:py-3 px-5 md:px-6 lg:px-5 rounded-full body-2-bold md:body-1-bold lg:body-2-bold hover:bg-[var(--color-neutral-6)] transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl min-h-[44px] md:min-h-[48px] lg:min-h-[44px]"
            @click="handleViewMore"
          >
            View more
          </Button>
        </div>
      </div>

      <!-- Height expansion content -->
      <div
        v-if="hoverEffect === 'height'"
        ref="expandedContent"
        class="absolute bottom-0 left-0 right-0 bg-[var(--color-neutral-1)] p-4 md:p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-700 ease-out opacity-0 group-hover:opacity-100"
      >
        <!-- Tech Stack Badges -->
        <div v-if="techStack && techStack.length" class="mb-3">
          <h4 class="label-2 text-[var(--color-primary-2)] mb-2">Technologies</h4>
          <div class="flex flex-wrap gap-1">
            <span
              v-for="tech in techStack.slice(0, 6)"
              :key="tech"
              class="px-2 py-1 bg-[var(--color-primary-3)] text-[var(--color-neutral-1)] body-3-bold rounded-full text-xs"
            >
              {{ tech }}
            </span>
          </div>
        </div>

        <!-- Project Info -->
        <div class="grid grid-cols-1 gap-2">
          <div v-if="duration">
            <span class="body-3-bold text-[var(--color-neutral-7)]">Duration:</span>
            <span class="body-3-regular text-[var(--color-neutral-5)] ml-2">{{ duration }}</span>
          </div>
          <div v-if="achievements">
            <span class="body-3-bold text-[var(--color-neutral-7)]">Achievement:</span>
            <span class="body-3-regular text-[var(--color-neutral-5)] ml-2">{{ achievements }}</span>
          </div>
        </div>
      </div>

      <!-- Sliding panel content -->
      <div
        v-if="hoverEffect === 'slide'"
        ref="slidingPanel"
        class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-[var(--color-neutral-1)] via-[var(--color-neutral-1)] to-transparent p-4 md:p-6 transform translate-y-full transition-transform duration-500 ease-out"
      >
        <div class="space-y-3">
          <!-- Project Metrics -->
          <div class="grid grid-cols-2 gap-2 text-center">
            <div v-if="githubStats?.stars !== undefined" class="bg-[var(--color-neutral-2)] rounded-lg p-2">
              <div class="body-2-bold text-[var(--color-primary-2)]">{{ githubStats.stars }}</div>
              <div class="body-3-regular text-[var(--color-neutral-5)]">Stars</div>
            </div>
            <div v-if="githubStats?.forks !== undefined" class="bg-[var(--color-neutral-2)] rounded-lg p-2">
              <div class="body-2-bold text-[var(--color-primary-2)]">{{ githubStats.forks }}</div>
              <div class="body-3-regular text-[var(--color-neutral-5)]">Forks</div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="flex gap-2">
            <Button
              variant="primary"
              class="flex-1 py-2 px-3 rounded-full body-3-bold"
              @click="handleViewMore"
            >
              View
            </Button>
            <Button
              v-if="githubUrl"
              variant="outline"
              class="flex-1 py-2 px-3 rounded-full body-3-bold"
              @click="handleGithubClick"
            >
              GitHub
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Button from '../atoms/Button.vue';
import { useAnimations } from '~/composables/useAnimations';

interface GitHubStats {
  stars?: number;
  forks?: number;
}

interface Props {
  name: string;
  category: string;
  imageUrl: string;
  url: string;
  variant?: 'large' | 'small';
  gradient?: string;
  glassEffect?: boolean;
  overlayColor?: string;
  glassBgColor?: string;
  hoverEffect?: 'height' | 'slide' | 'flip' | 'default';
  techStack?: string[];
  description?: string;
  duration?: string;
  achievements?: string;
  githubUrl?: string;
  githubStats?: GitHubStats;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'large',
  glassEffect: false,
  hoverEffect: 'default'
});

const emit = defineEmits<{
  viewMore: [url: string];
  githubClick: [url: string];
}>();

// Template refs
const cardElement = ref<HTMLElement | null>(null);
const cardFront = ref<HTMLElement | null>(null);
const cardBack = ref<HTMLElement | null>(null);
const expandedContent = ref<HTMLElement | null>(null);
const slidingPanel = ref<HTMLElement | null>(null);

// Reactive state
const isFlipped = ref(false);
const isHovered = ref(false);

// Animation composable
const { fadeInOnScroll } = useAnimations();

// Computed style for background image and overlays
const backgroundStyle = computed(() => {
  let style: any = {
    backgroundImage: `url(${props.imageUrl})`
  };

  // For large cards, add overlay color if specified
  if (props.variant === 'large' && props.overlayColor) {
    style.backgroundImage = `linear-gradient(${props.overlayColor}, ${props.overlayColor}), url(${props.imageUrl})`;
  }

  return style;
});

// Computed style for overlay effects on small cards
const overlayStyle = computed(() => {
  if (props.variant === 'small') {
    if (props.gradient) {
      return { background: props.gradient };
    } else if (props.overlayColor) {
      return { backgroundColor: props.overlayColor };
    }
  }
  return {};
});

// Mouse event handlers
const handleMouseEnter = () => {
  if (import.meta.client) {
    isHovered.value = true;
    
    if (props.hoverEffect === 'flip') {
      isFlipped.value = true;
    } else if (props.hoverEffect === 'slide' && slidingPanel.value) {
      // GSAP animation for sliding panel
      gsap.to(slidingPanel.value, {
        y: 0,
        duration: 0.5,
        ease: 'power2.out'
      });
    } else if (props.hoverEffect === 'height' && expandedContent.value) {
      // GSAP animation for height expansion content
      gsap.to(expandedContent.value, {
        y: 0,
        opacity: 1,
        duration: 0.7,
        ease: 'power2.out'
      });
    }
  }
};

const handleMouseLeave = () => {
  if (import.meta.client) {
    isHovered.value = false;
    
    if (props.hoverEffect === 'flip') {
      isFlipped.value = false;
    } else if (props.hoverEffect === 'slide' && slidingPanel.value) {
      // Reset sliding panel
      gsap.to(slidingPanel.value, {
        y: '100%',
        duration: 0.3,
        ease: 'power2.in'
      });
    } else if (props.hoverEffect === 'height' && expandedContent.value) {
      // Reset height expansion content
      gsap.to(expandedContent.value, {
        y: '100%',
        opacity: 0,
        duration: 0.4,
        ease: 'power2.in'
      });
    }
  }
};

const handleViewMore = () => {
  emit('viewMore', props.url);
  window.open(props.url, '_blank');
};

const handleGithubClick = () => {
  if (props.githubUrl) {
    emit('githubClick', props.githubUrl);
    window.open(props.githubUrl, '_blank');
  }
};

// Initialize animations on mount
onMounted(() => {
  if (import.meta.client && cardElement.value) {
    // Set initial states for animated elements
    if (props.hoverEffect === 'slide' && slidingPanel.value) {
      gsap.set(slidingPanel.value, { y: '100%' });
    }
    if (props.hoverEffect === 'height' && expandedContent.value) {
      gsap.set(expandedContent.value, { y: '100%', opacity: 0 });
    }
    
    // Add scroll-triggered fade in animation
    fadeInOnScroll(cardElement.value, {
      start: 'top 85%',
      duration: 0.6,
      ease: 'power2.out'
    });
  }
});
</script>
